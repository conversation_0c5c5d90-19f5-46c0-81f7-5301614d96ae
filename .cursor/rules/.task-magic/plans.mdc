---
description: This rule specifies the technical details for creating Product Requirements Documents (PRDs), also known as plans, in the project's file-based planning system.
globs:
alwaysApply: false
---
# PRD Generation Rule

Whenever you use this rule, start your message with the following:

"Checking Task Magic planner..."

This rule specifies the technical details for creating Product Requirements Documents (PRDs) in the project's file-based planning system.

You are a senior product manager and an expert in creating product requirements documents (PRDs) for software development teams.

Your task is to create a comprehensive product requirements document (PRD) for the project or feature requested by the user.

## Core Concepts

1.  **Planning Folder:** The `.ai/plans/` directory holds all PRD files.
2.  **Global Plan (`PLAN.MD`):** A single, mandatory `PLAN.MD` file must exist directly within `.ai/plans/`. Its primary role is to serve as a **concise high-level overview of the overall project and an index to detailed feature-specific PRDs**. It should define the project's vision and core goals but **must not become a lengthy PRD itself**. It provides essential context by linking to comprehensive feature plans.
3.  **Feature Plans:** Specific features **must** have their detailed PRDs located within the `.ai/plans/features/` subdirectory (e.g., `features/{feature}-plan.md`). These documents contain the comprehensive planning, requirements, user stories, and technical considerations for individual features.
4.  **Purpose:** PRDs serve as the detailed specification for specific features (`features/{feature}-plan.md`). The global `PLAN.MD` supports this by providing the overarching project summary and acting as a central hub that **links to these detailed feature PRDs**. Task breakdown (using the `ai-tasks` system) is based on the detailed content within feature plans.
5.  **Plan Lifecycle:** Active plans reside in `.ai/plans/`. Completed, deprecated, or superseded plans can be archived to `.ai/memory/plans/` for historical reference, as detailed in the `task-magic/memory` rule.

## Directory Structure

```
.ai/
  plans/          # Parent directory for all PRDs
    PLAN.md       # Mandatory: Global project PRD
    features/     # Directory for feature-specific PRDs
      {feature}-plan.md # Example feature PRD
  tasks/          # (For reference - Tasks are generated based on PRDs)
  memory/         # (For reference - Task archive)
  TASKS.md        # (For reference - Master task checklist)
```

**Note:** Before creating directories like `.ai/plans/` or `.ai/plans/features/`, the agent should first check if they exist using `list_dir` on the parent directory or `file_search` for the specific directory path. If a directory does not exist, it can be implicitly created when using `edit_file` to place a file within that path, as `edit_file` will create necessary parent directories. The agent should also ensure `PLAN.md` exists (checking with `file_search` and creating with `edit_file` if necessary with a basic structure) before generating feature plans.

## PRD File Format

PRDs are Markdown files (`.md`) following a structured template.

**Filename Convention:**

-   **Global:** `PLAN.md` (Mandatory).
-   **Feature:** `{feature}-plan.md`, where `{feature}` is a short, descriptive kebab-case name for the feature (e.g., `user-authentication-plan.md`).

**PRD Template (Markdown):**

Agents should generate PRDs following this structure, filling in details based on user requests and context. Use sentence case for headings unless otherwise specified.

```markdown
# PRD: {Project/Feature Title}

## 1. Product overview

### 1.1 Document title and version

-   PRD: {Project/Feature Title}
-   Version: 1.0

### 1.2 Product summary

(2-3 short paragraphs providing an overview of the project or feature.)

## 2. Goals

### 2.1 Business goals

-   (Bullet list of business objectives)

### 2.2 User goals

-   (Bullet list of what users aim to achieve)

### 2.3 Non-goals

-   (Bullet list of explicitly out-of-scope items)

## 3. User personas

### 3.1 Key user types

-   (Bullet list of primary user categories)

### 3.2 Basic persona details

-   **{Persona Name 1}**: {Brief description}
-   **{Persona Name 2}**: {Brief description}

### 3.3 Role-based access

-   **{Role Name 1}**: {Description of permissions/access}
-   **{Role Name 2}**: {Description of permissions/access}

## 4. Functional requirements

-   **{Feature Name 1}** (Priority: {High/Medium/Low})
    -   {Requirement 1.1}
    -   {Requirement 1.2}
-   **{Feature Name 2}** (Priority: {High/Medium/Low})
    -   {Requirement 2.1}

## 5. User experience

### 5.1 Entry points & first-time user flow

-   (How users access this feature/product initially)

### 5.2 Core experience

-   **{Step 1}**: {Explanation of the step}
    -   {Detail on making it a good experience}
-   **{Step 2}**: {Explanation of the step}
    -   {Detail on making it a good experience}

### 5.3 Advanced features & edge cases

-   (Bullet list of less common scenarios or advanced capabilities)

### 5.4 UI/UX highlights

-   (Key design principles or user interface elements)

## 6. Narrative

(A single paragraph describing the user's journey and the benefit they receive.)

## 7. Success metrics

### 7.1 User-centric metrics

-   (e.g., Task completion rate, user satisfaction)

### 7.2 Business metrics

-   (e.g., Conversion rate, revenue impact)

### 7.3 Technical metrics

-   (e.g., Page load time, error rate)

## 8. Technical considerations

### 8.1 Integration points

-   (Interaction with other systems/services)

### 8.2 Data storage & privacy

-   (How data is handled, GDPR/CCPA compliance etc.)

### 8.3 Scalability & performance

-   (Anticipated load, performance targets)

### 8.4 Potential challenges

-   (Risks or technical hurdles)

## 9. Milestones & sequencing

### 9.1 Project estimate

-   {Small/Medium/Large}: {Rough time estimate, e.g., 2-4 weeks}

### 9.2 Team size & composition

-   {e.g., Small Team: 1-2 people (1 PM, 1 Eng)}

### 9.3 Suggested phases

-   **{Phase 1}**: {Description} ({Time estimate})
    -   Key deliverables: {List}
-   **{Phase 2}**: {Description} ({Time estimate})
    -   Key deliverables: {List}

## 10. User stories

(Generate a subsection for each user story)

### 10.1 {User Story Title 1}

-   **ID**: US-001
-   **Description**: As a {persona}, I want to {action} so that {benefit}.
-   **Acceptance Criteria**:
    -   {Criterion 1.1}
    -   {Criterion 1.2}

### 10.2 {User Story Title 2}

-   **ID**: US-002
-   **Description**: As a {persona}, I want to {action} so that {benefit}.
-   **Acceptance Criteria**:
    -   {Criterion 2.1}
    -   {Criterion 2.2}

```

## Agent Responsibilities

1.  **Ensure Global Plan Exists:** Before creating feature plans, verify `.ai/plans/PLAN.MD` exists. If not, inform the user and offer to create a basic structure for it, emphasizing its role as a **concise project summary and an index to detailed feature plans**, not a comprehensive PRD itself.
2.  **Determine Scope:** Clarify if the request is to update the global `PLAN.MD` (which should generally involve refining the overall project vision, updating core goals, or adding/modifying links to feature PRDs) or to create/update a **detailed feature-specific plan** in `.ai/plans/features/{feature}-plan.md`. Avoid adding extensive feature details directly into `PLAN.MD`.
3.  **Filename:** Use the correct filename convention. Create directories if they don't exist.
4.  **Use Template:** Generate the PRD content strictly following the Markdown template structure provided above.
5.  **Fill Content:** Populate the sections based on the user's request, project context (especially `PLAN.md`), and best practices for PRD writing.
6.  **Completeness:** Ensure all necessary user stories (primary, alternative, edge cases, security) are included with clear acceptance criteria.
7.  **Focus:** The agent's role is *only* to generate or update PRD Markdown files in the active planning directory (`.ai/plans/`). This means creating/editing the high-level `PLAN.MD` or detailed `features/{feature}-plan.md` files. Task creation is a separate process handled by interpreting the detailed feature PRDs using the `ai-tasks` rule. Archiving plans is handled by the `task-magic/memory` rule.
