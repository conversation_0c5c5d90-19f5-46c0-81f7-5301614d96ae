/**
 * Root Layout Component
 *
 * This is the top-level layout component for the entire application. It wraps all pages
 * and provides the fundamental HTML structure, global styles, fonts, and context providers.
 *
 * The layout implements:
 * - Geist Sans and Geist Mono fonts from Google Fonts for consistent typography
 * - Global CSS styles imported from the styles directory
 * - Application-wide providers for state management and theming
 * - Basic HTML structure with proper language attribute
 * - Metadata for SEO and browser tabs
 * - Stagewise toolbar for AI-powered UI editing in development mode
 */

import type { Metadata } from 'next';
import { <PERSON>eist, Geist_Mono } from 'next/font/google';
import type React from 'react';
import '@/styles/globals.css';
import { StagewiseToolbar } from '@stagewise/toolbar-next';
import { Providers } from './(dashboard)/providers';

/**
 * Stagewise toolbar configuration
 * Only used in development mode
 */
const stagewiseConfig = {
  plugins: [],
};

/**
 * Geist Sans font configuration
 * Loads the Geist Sans font and makes it available via CSS variables
 *
 * @type {import('next/font/google').FontVariable}
 */
const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
});

/**
 * Geist Mono font configuration
 * Loads the Geist Mono font for monospace text and makes it available via CSS variables
 *
 * @type {import('next/font/google').FontVariable}
 */
const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
});

/**
 * Application metadata configuration
 * Defines the default title and description for all pages
 * These can be overridden by individual page components
 */
export const metadata: Metadata = {
  title: 'Create Next App',
  description: 'Generated by create next app',
};

/**
 * Root Layout Component
 *
 * Provides the base HTML structure for all pages in the application.
 * Applies global fonts, includes the application-wide providers, and renders child components.
 *
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - The page content to render within the layout
 * @returns {JSX.Element} The complete HTML structure with providers and child content
 */
export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${geistSans.variable} ${geistMono.variable} antialiased`}>
        <Providers>{children}</Providers>
        {process.env.NODE_ENV === 'development' && <StagewiseToolbar config={stagewiseConfig} />}
      </body>
    </html>
  );
}
