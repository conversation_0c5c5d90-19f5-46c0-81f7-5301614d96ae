{"permissions": {"allow": ["WebFetch(domain:docs.anthropic.com)", "<PERSON><PERSON>(claude mcp:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(cp:*)", "Bash(pnpm prisma:generate:*)", "Bash(pnpm prisma migrate dev:*)", "Bash(pnpm tsx:*)", "Bash(pnpm test:*)", "Bash(pnpm lint:*)", "Bash(pnpm tsc:*)", "Bash(ls:*)", "mcp__supabase__list_projects", "Bash(supabase projects:*)", "mcp__supabase__list_migrations", "mcp__supabase__execute_sql", "mcp__supabase__apply_migration", "<PERSON><PERSON>(cat:*)", "<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(sed:*)", "<PERSON><PERSON>(mv:*)", "Bash(grep:*)", "Bash(rm:*)", "mcp__supabase__list_tables", "Bash(pnpm prisma:studio:*)", "Bash(pnpm exec tsc:*)", "Bash(pnpm build:*)", "mcp__supabase__get_project", "mcp__supabase__pause_project", "mcp__supabase__restore_project", "Bash(pnpm prisma:push:*)", "<PERSON><PERSON>(touch:*)", "mcp__ide__getDiagnostics", "mcp__think-tool__think", "Bash(pnpm prisma db push:*)", "Bash(find:*)", "Bash(pnpm dev:*)", "Bash(pnpm add:*)", "Bash(pnpm run tsc:*)", "mcp__puppeteer__puppeteer_navigate", "mcp__puppeteer__puppeteer_screenshot", "mcp__puppeteer__puppeteer_fill", "mcp__puppeteer__puppeteer_click", "mcp__puppeteer__puppeteer_evaluate", "Bash(pnpm prisma studio:*)", "WebFetch(domain:supabase.com)", "Bash(ss:*)", "Bash(pnpm biome check:*)", "Bash(rg:*)", "Bash(pnpm biome lint:*)", "WebFetch(domain:github.com)", "Bash(pnpm typecheck:*)", "Bash(pnpm exec biome lint:*)", "Bash(pnpm prisma migrate reset:*)", "Bash(pnpm prisma db seed:*)", "mcp__prisma__migrate-status", "mcp__prisma__migrate-dev", "Bash(pnpm dlx:*)", "Bash(pnpm prisma generate:*)", "Bash(pnpm prisma validate:*)", "Bash(pnpm db:push:*)", "mcp__sequential-thinking__sequentialthinking", "mcp__supabase__deploy_edge_function", "Bash(supabase functions:*)", "mcp__supabase__get_logs", "Bash(git push:*)", "Bash(git checkout:*)", "Bash(gh pr create:*)", "Bash(git add:*)", "<PERSON><PERSON>(gh pr edit:*)", "Bash(pnpm:*)", "Bash(node:*)", "Bash(npx tsc:*)", "Bash(gh pr view:*)", "Bash(gh api:*)", "Bash(gh issue create:*)", "WebFetch(domain:linear.app)", "Bash(gh pr list:*)", "Bash(git commit:*)", "Bash(for file in \"src/app/(auth)/reset-password/error.tsx\" \"src/app/(auth)/reset-password/loading.tsx\" \"src/app/(auth)/callback/page.tsx\" \"src/app/(auth)/login/error.tsx\" \"src/app/(auth)/login/loading.tsx\")", "Bash(do if [ -f \"$file\" ])", "Bash(then sed -i.bak 's/class=\"\"/className=\"\"/g' \"$file\")", "Bash(echo \"Fixed $file\")", "Bash(fi)", "Bash(done)", "<PERSON><PERSON>(head:*)", "WebFetch(domain:biomejs.dev)", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "Bash(git fetch:*)", "mcp__zen__thinkdeep", "mcp__zen__chat", "mcp__zen__debug", "mcp__zen__codereview"], "deny": []}, "enableAllProjectMcpServers": false, "enabledMcpjsonServers": ["supabase"]}